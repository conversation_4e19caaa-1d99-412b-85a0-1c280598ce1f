This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: yolo_cea_paper.aux
The style file: IEEEtran.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: reference.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 44 entries,
            4087 wiz_defined-function locations,
            1054 strings with 15786 characters,
and the built_in function-call counts, 32381 in all, are:
= -- 2568
> -- 912
< -- 215
+ -- 503
- -- 202
* -- 1539
:= -- 4791
add.period$ -- 90
call.type$ -- 44
change.case$ -- 48
chr.to.int$ -- 489
cite$ -- 44
duplicate$ -- 2393
empty$ -- 2605
format.name$ -- 216
if$ -- 7548
int.to.chr$ -- 0
int.to.str$ -- 44
missing$ -- 442
newline$ -- 159
num.names$ -- 44
pop$ -- 1090
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 2499
stack$ -- 0
substring$ -- 1320
swap$ -- 1872
text.length$ -- 52
text.prefix$ -- 0
top$ -- 5
type$ -- 44
warning$ -- 0
while$ -- 118
width$ -- 46
write$ -- 436
